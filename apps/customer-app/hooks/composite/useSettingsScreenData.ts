import { useAuth } from '@indie-points/contexts';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useCallback, useState } from 'react';
import { Alert } from 'react-native';

export function useSettingsScreenData() {
  const { user, signOut, deleteAccount, loading } = useAuth();
  const [deletingAccount, setDeletingAccount] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSignOut = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert('Sign out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign out',
        style: 'destructive',
        onPress: async () => {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          const { error } = await signOut();
          if (!error) {
            router.replace('/(auth)/sign-in');
          }
        },
      },
    ]);
  }, [signOut]);

  const handleDeleteAccount = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Close Account',
      'Are you sure you want to close your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Continue',
          style: 'destructive',
          onPress: () => showFinalConfirmation(),
        },
      ]
    );
  }, []);

  const showFinalConfirmation = useCallback(() => {
    Alert.alert(
      '⚠️ Final Warning',
      'This will permanently delete:\n\n• Your customer profile\n• All your points and rewards\n• Your complete transaction history\n• Your login credentials\n\n⚠️ This action cannot be undone',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete Everything',
          style: 'destructive',
          onPress: async () => {
            console.log('🔥 User confirmed account deletion');
            setDeletingAccount(true);
            setError(null);

            try {
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
              console.log('📞 Calling deleteAccount from AuthContext...');

              const result = await deleteAccount();

              console.log('📋 Delete account result:', result);

              if (result.error) {
                console.error('❌ Account deletion failed:', result.error);
                setError(result.error.message || 'Failed to delete account');
                setDeletingAccount(false);
              } else {
                console.log('✅ Account deletion successful');
                await Haptics.notificationAsync(
                  Haptics.NotificationFeedbackType.Success
                );
              }
            } catch (error) {
              console.error('💥 Unexpected error deleting account:', error);
              setError('Failed to delete account. Please try again.');
              setDeletingAccount(false);
            }
          },
        },
      ]
    );
  }, [deleteAccount]);

  return {
    user,
    loading,
    deletingAccount,
    error,
    handleSignOut,
    handleDeleteAccount,
  };
}
